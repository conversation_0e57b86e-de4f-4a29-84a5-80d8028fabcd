package com.tinyzk.user.center.config;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云OSS配置
 */
@Configuration
@ConfigurationProperties(prefix = "aliyun.oss")
@Data
@Slf4j
public class OSSConfig {

    /**
     * 外网端点
     */
    private String endpoint;

    /**
     * 内网端点（提升上传下载速度）
     */
    private String internalEndpoint;

    /**
     * 访问密钥ID
     */
    private String accessKeyId;

    /**
     * 访问密钥Secret
     */
    private String accessKeySecret;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectionTimeout = 3000;

    /**
     * Socket超时时间（毫秒）
     */
    private int socketTimeout = 30000;

    /**
     * 最大连接数
     */
    private int maxConnections = 50;

    /**
     * 最大错误重试次数
     */
    private int maxErrorRetry = 3;

    /**
     * 是否使用内网端点
     */
    private boolean useInternalEndpoint = false;

    /**
     * 创建OSS客户端
     */
    @Bean
    public OSS ossClient() {
        // 优化的客户端配置
        ClientBuilderConfiguration config = new ClientBuilderConfiguration();
        config.setMaxConnections(maxConnections);
        config.setSocketTimeout(socketTimeout);
        config.setConnectionTimeout(connectionTimeout);
        config.setMaxErrorRetry(maxErrorRetry);

        // 启用CRC校验
        config.setCrcCheckEnabled(true);

        // 选择端点
        String actualEndpoint = useInternalEndpoint && internalEndpoint != null ? internalEndpoint : endpoint;

        return new OSSClientBuilder().build(actualEndpoint, accessKeyId, accessKeySecret, config);
    }
}
