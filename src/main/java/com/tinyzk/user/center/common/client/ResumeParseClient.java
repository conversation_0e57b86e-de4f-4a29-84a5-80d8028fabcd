package com.tinyzk.user.center.common.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSONObject;

/**
 * 小析智能-简历解析
 * 
 * <AUTHOR>
 * @since 2025-06-19
 */
@FeignClient(
    name = "resume-parse-client",
    url = "${resume.parse.api-url}",
    configuration = ResumeParseClientConfiguration.class
)
public interface ResumeParseClient {

    /**
     * 简历解析-文件
     */
    @PostMapping(value = "/parse_file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    String parseFile(@RequestPart("file") MultipartFile file,
                     @RequestParam("rawtext") int rawtext,
                     @RequestParam("handle_image") int handleImage,
                     @RequestParam("avatar") int avatar,
                     @RequestParam("parse_mode") String parseMode,
                     @RequestParam("ocr_mode") String ocrMode,
                     @RequestParam("ocr_service") String ocrService);


    /**
     * 简历解析-base64
     */
    @PostMapping("/parse_base")
    String parseBase(@RequestBody JSONObject jsonObject);


    /**
     * 简历解析-人像分析(文件)
     */
    @PostMapping(value = "/analyze_base", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    String analyzeFile(@RequestPart("file") MultipartFile file);


    /**
     * 简历解析-人像分析(解析后简历)
     */
    @PostMapping("/analyze_json")
    String analyzeBase(@RequestBody JSONObject jsonObject);
}
