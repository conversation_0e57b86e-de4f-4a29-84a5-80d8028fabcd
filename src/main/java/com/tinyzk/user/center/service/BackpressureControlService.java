package com.tinyzk.user.center.service;

import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 背压控制服务
 * 根据系统负载动态调整消息处理速率
 */
@Service
@Slf4j
public class BackpressureControlService {

    private final ThreadPoolTaskExecutor batchResumeParseExecutor;
    private final ThreadPoolTaskExecutor apiCallExecutor;
    private final MeterRegistry meterRegistry;

    // 动态限流器
    private volatile RateLimiter dynamicRateLimiter;
    
    // 系统负载指标
    private final AtomicInteger currentLoad = new AtomicInteger(0);
    private final AtomicLong rejectedMessages = new AtomicLong(0);
    private final AtomicLong processedMessages = new AtomicLong(0);
    
    // 背压控制参数
    private volatile int baseRateLimit = 30; // 基础限流速率
    private volatile double loadThreshold = 0.8; // 负载阈值
    private volatile boolean backpressureEnabled = false;

    public BackpressureControlService(@Qualifier("batchResumeParseExecutor") ThreadPoolTaskExecutor batchExecutor,
                                    @Qualifier("apiCallExecutor") ThreadPoolTaskExecutor apiExecutor,
                                    MeterRegistry meterRegistry) {
        this.batchResumeParseExecutor = batchExecutor;
        this.apiCallExecutor = apiExecutor;
        this.meterRegistry = meterRegistry;
        
        // 初始化动态限流器
        this.dynamicRateLimiter = createRateLimiter(baseRateLimit);
        
        // 注册监控指标
        registerMetrics();
    }

    

    

    /**
     * 检查是否应该启用背压控制
     */
    public boolean shouldEnableBackpressure() {
        double systemLoad = calculateSystemLoad();
        boolean shouldEnable = systemLoad > loadThreshold;
        
        if (shouldEnable != backpressureEnabled) {
            backpressureEnabled = shouldEnable;
            log.info("背压控制状态变更: enabled={}, systemLoad={:.2f}", backpressureEnabled, systemLoad);
            meterRegistry.counter("backpressure.state.change", 
                "enabled", String.valueOf(backpressureEnabled)).increment();
        }
        
        return backpressureEnabled;
    }

    /**
     * 计算系统负载
     */
    private double calculateSystemLoad() {
        // 线程池负载
        double batchPoolLoad = calculatePoolLoad(batchResumeParseExecutor);
        double apiPoolLoad = calculatePoolLoad(apiCallExecutor);
        
        // 系统内存负载
        Runtime runtime = Runtime.getRuntime();
        double memoryLoad = (double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.maxMemory();
        
        // 综合负载计算（加权平均）
        double systemLoad = (batchPoolLoad * 0.4 + apiPoolLoad * 0.3 + memoryLoad * 0.3);
        
        currentLoad.set((int) (systemLoad * 100));
        return systemLoad;
    }

    /**
     * 计算线程池负载
     */
    private double calculatePoolLoad(ThreadPoolTaskExecutor executor) {
        int activeCount = executor.getActiveCount();
        int maxPoolSize = executor.getMaxPoolSize();
        int queueSize = executor.getQueueSize();
        int queueCapacity = executor.getQueueCapacity();
        
        // 线程使用率
        double threadUsage = maxPoolSize > 0 ? (double) activeCount / maxPoolSize : 0.0;
        
        // 队列使用率
        double queueUsage = queueCapacity > 0 ? (double) queueSize / queueCapacity : 0.0;
        
        // 返回较高的使用率
        return Math.max(threadUsage, queueUsage);
    }

    /**
     * 动态调整限流速率
     */
    @Scheduled(fixedDelay = 10000) // 每10秒调整一次
    public void adjustRateLimit() {
        try {
            double systemLoad = calculateSystemLoad();
            int newRateLimit = calculateNewRateLimit(systemLoad);
            
            if (newRateLimit != getCurrentRateLimit()) {
                updateRateLimiter(newRateLimit);
                log.info("动态调整限流速率: oldRate={}, newRate={}, systemLoad={:.2f}", 
                        getCurrentRateLimit(), newRateLimit, systemLoad);
            }
            
        } catch (Exception e) {
            log.error("动态调整限流速率失败", e);
        }
    }

    /**
     * 尝试获取处理许可
     */
    public boolean tryAcquirePermit() {
        boolean permitted = dynamicRateLimiter.acquirePermission();
        if (permitted) {
            processedMessages.incrementAndGet();
        } else {
            rejectedMessages.incrementAndGet();
        }
        return permitted;
    }

    /**
     * 计算新的限流速率
     */
    private int calculateNewRateLimit(double systemLoad) {
        if (systemLoad < 0.3) {
            // 低负载，提高限流速率
            return Math.min(baseRateLimit * 2, 100);
        } else if (systemLoad < 0.6) {
            // 中等负载，保持基础速率
            return baseRateLimit;
        } else if (systemLoad < 0.8) {
            // 高负载，降低限流速率
            return Math.max(baseRateLimit / 2, 5);
        } else {
            // 超高负载，大幅降低限流速率
            return Math.max(baseRateLimit / 4, 2);
        }
    }

    /**
     * 更新限流器
     */
    private void updateRateLimiter(int newRateLimit) {
        this.dynamicRateLimiter = createRateLimiter(newRateLimit);
        meterRegistry.counter("backpressure.rate_limit.adjusted").increment();
    }

    /**
     * 创建限流器
     */
    private RateLimiter createRateLimiter(int rateLimit) {
        return RateLimiter.of("dynamic-backpressure",
            RateLimiterConfig.custom()
                .limitForPeriod(rateLimit)
                .limitRefreshPeriod(Duration.ofSeconds(1))
                .timeoutDuration(Duration.ofMillis(100))
                .build());
    }

    /**
     * 获取当前限流速率
     */
    public int getCurrentRateLimit() {
        return dynamicRateLimiter.getRateLimiterConfig().getLimitForPeriod();
    }

    /**
     * 注册监控指标
     */
    private void registerMetrics() {
        // 系统负载指标
        Gauge.builder("backpressure.system.load", currentLoad, AtomicInteger::get)
            .description("Current system load percentage")
            .register(meterRegistry);

        // 背压状态指标
        Gauge.builder("backpressure.enabled", this, service -> service.backpressureEnabled ? 1 : 0)
            .description("Whether backpressure is currently enabled")
            .register(meterRegistry);

        // 当前限流速率指标
        Gauge.builder("backpressure.rate_limit", this, BackpressureControlService::getCurrentRateLimit)
            .description("Current rate limit per second")
            .register(meterRegistry);

        // 处理消息数指标
        Gauge.builder("backpressure.processed.messages", processedMessages, AtomicLong::get)
            .description("Total processed messages")
            .register(meterRegistry);

        // 拒绝消息数指标
        Gauge.builder("backpressure.rejected.messages", rejectedMessages, AtomicLong::get)
            .description("Total rejected messages")
            .register(meterRegistry);

        log.info("背压控制监控指标注册完成");
    }

    /**
     * 获取背压统计信息
     */
    public BackpressureStatistics getStatistics() {
        BackpressureStatistics stats = new BackpressureStatistics();
        stats.setSystemLoad(currentLoad.get() / 100.0);
        stats.setBackpressureEnabled(backpressureEnabled);
        stats.setCurrentRateLimit(getCurrentRateLimit());
        stats.setProcessedMessages(processedMessages.get());
        stats.setRejectedMessages(rejectedMessages.get());
        stats.setBatchPoolLoad(calculatePoolLoad(batchResumeParseExecutor));
        stats.setApiPoolLoad(calculatePoolLoad(apiCallExecutor));
        
        Runtime runtime = Runtime.getRuntime();
        stats.setMemoryUsage((double) (runtime.totalMemory() - runtime.freeMemory()) / runtime.maxMemory());
        
        return stats;
    }

    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        processedMessages.set(0);
        rejectedMessages.set(0);
        log.info("背压控制统计信息已重置");
    }

    /**
     * 背压统计信息
     */
    public static class BackpressureStatistics {
        private double systemLoad;
        private boolean backpressureEnabled;
        private int currentRateLimit;
        private long processedMessages;
        private long rejectedMessages;
        private double batchPoolLoad;
        private double apiPoolLoad;
        private double memoryUsage;

        // Getters and Setters
        public double getSystemLoad() { return systemLoad; }
        public void setSystemLoad(double systemLoad) { this.systemLoad = systemLoad; }
        public boolean isBackpressureEnabled() { return backpressureEnabled; }
        public void setBackpressureEnabled(boolean backpressureEnabled) { this.backpressureEnabled = backpressureEnabled; }
        public int getCurrentRateLimit() { return currentRateLimit; }
        public void setCurrentRateLimit(int currentRateLimit) { this.currentRateLimit = currentRateLimit; }
        public long getProcessedMessages() { return processedMessages; }
        public void setProcessedMessages(long processedMessages) { this.processedMessages = processedMessages; }
        public long getRejectedMessages() { return rejectedMessages; }
        public void setRejectedMessages(long rejectedMessages) { this.rejectedMessages = rejectedMessages; }
        public double getBatchPoolLoad() { return batchPoolLoad; }
        public void setBatchPoolLoad(double batchPoolLoad) { this.batchPoolLoad = batchPoolLoad; }
        public double getApiPoolLoad() { return apiPoolLoad; }
        public void setApiPoolLoad(double apiPoolLoad) { this.apiPoolLoad = apiPoolLoad; }
        public double getMemoryUsage() { return memoryUsage; }
        public void setMemoryUsage(double memoryUsage) { this.memoryUsage = memoryUsage; }
    }
}
