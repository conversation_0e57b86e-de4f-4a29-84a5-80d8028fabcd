package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.ResumeParseMessage;
import com.tinyzk.user.center.dto.ThirdPartyParseResultDTO;
import com.tinyzk.user.center.entity.*;
import com.tinyzk.user.center.mapper.*;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class ResumePersistenceService {

    private final ResumeDataConversionService conversionService;
    private final BatchDatabaseService batchDatabaseService;
    private final UserProfileMapper userProfileMapper;
    private final UserContactMethodsMapper userContactMethodsMapper;
    private final UserEducationHistoryMapper userEducationHistoryMapper;
    private final UserWorkHistoryMapper userWorkHistoryMapper;
    private final UserProjectHistoryMapper userProjectHistoryMapper;
    private final UserSkillsMapper userSkillsMapper;
    private final UserTrainingMapper userTrainingMapper;
    private final MeterRegistry meterRegistry;

    public ResumePersistenceService(ResumeDataConversionService conversionService,
                                    BatchDatabaseService batchDatabaseService,
                                    UserProfileMapper userProfileMapper,
                                    UserContactMethodsMapper userContactMethodsMapper,
                                    UserEducationHistoryMapper userEducationHistoryMapper,
                                    UserWorkHistoryMapper userWorkHistoryMapper,
                                    UserProjectHistoryMapper userProjectHistoryMapper,
                                    UserSkillsMapper userSkillsMapper,
                                    UserTrainingMapper userTrainingMapper,
                                    MeterRegistry meterRegistry) {
        this.conversionService = conversionService;
        this.batchDatabaseService = batchDatabaseService;
        this.userProfileMapper = userProfileMapper;
        this.userContactMethodsMapper = userContactMethodsMapper;
        this.userEducationHistoryMapper = userEducationHistoryMapper;
        this.userWorkHistoryMapper = userWorkHistoryMapper;
        this.userProjectHistoryMapper = userProjectHistoryMapper;
        this.userSkillsMapper = userSkillsMapper;
        this.userTrainingMapper = userTrainingMapper;
        this.meterRegistry = meterRegistry;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveConvertedData(Long userId, ThirdPartyParseResultDTO parseResult, ResumeParseMessage message) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            log.info("开始转换和保存简历数据: userId={}, messageId={}", userId, message.getMessageId());

            if (parseResult.getParsingResult() == null) {
                log.warn("解析结果为空，跳过数据转换: userId={}", userId);
                return;
            }

            var parsingResult = parseResult.getParsingResult();
            int totalSavedRecords = 0;

            if (parsingResult.getBasicInfo() != null) {
                totalSavedRecords += saveUserProfile(userId, parsingResult.getBasicInfo());
            }
            if (parsingResult.getContactInfo() != null) {
                totalSavedRecords += saveContactMethods(userId, parsingResult.getContactInfo());
            }
            if (parsingResult.getEducationExperience() != null && !parsingResult.getEducationExperience().isEmpty()) {
                totalSavedRecords += saveEducationHistory(userId, parsingResult.getEducationExperience());
            }
            if (parsingResult.getWorkExperience() != null && !parsingResult.getWorkExperience().isEmpty()) {
                totalSavedRecords += saveWorkHistory(userId, parsingResult.getWorkExperience());
            }
            if (parsingResult.getProjectExperience() != null && !parsingResult.getProjectExperience().isEmpty()) {
                totalSavedRecords += saveProjectHistory(userId, parsingResult.getProjectExperience());
            }
            if (parsingResult.getTrainingExperience() != null && !parsingResult.getTrainingExperience().isEmpty()) {
                totalSavedRecords += saveTrainingHistory(userId, parsingResult.getTrainingExperience());
            }
            if (parsingResult.getOthers() != null) {
                totalSavedRecords += saveSkills(userId, parsingResult.getOthers());
            }

            log.info("数据转换和保存完成: userId={}, messageId={}, 总保存记录数={}",
                    userId, message.getMessageId(), totalSavedRecords);

            meterRegistry.counter("resume.data.conversion.success").increment();
            meterRegistry.counter("resume.data.records.saved", "user_id", userId.toString())
                    .increment(totalSavedRecords);

        } catch (Exception e) {
            log.error("数据转换和保存失败: userId={}, messageId={}", userId, message.getMessageId(), e);
            meterRegistry.counter("resume.data.conversion.failure").increment();
            meterRegistry.counter("resume.data.conversion.error",
                    "error_type", e.getClass().getSimpleName()).increment();
            throw new RuntimeException("简历数据保存失败: " + e.getMessage(), e);
        } finally {
            sample.stop(Timer.builder("resume.data.conversion.duration")
                    .tag("user_id", userId.toString())
                    .register(meterRegistry));
        }
    }

    private int saveUserProfile(Long userId, ThirdPartyParseResultDTO.BasicInfo basicInfo) {
        try {
            UserProfile userProfile = conversionService.convertBasicInfo(userId, basicInfo);
            if (userProfile == null) {
                log.debug("基本信息转换结果为空，跳过保存: userId={}", userId);
                return 0;
            }

            UserProfile existingProfile = userProfileMapper.selectByUserId(userId);
            int savedCount = 0;

            if (existingProfile != null) {
                if (userProfile.getNickname() != null) existingProfile.setNickname(userProfile.getNickname());
                if (userProfile.getGender() != null) existingProfile.setGender(userProfile.getGender());
                if (userProfile.getBirthday() != null) existingProfile.setBirthday(userProfile.getBirthday());
                if (userProfile.getRegionName() != null) existingProfile.setRegionName(userProfile.getRegionName());
                if (userProfile.getAddress() != null) existingProfile.setAddress(userProfile.getAddress());
                if (userProfile.getBio() != null) existingProfile.setBio(userProfile.getBio());
                savedCount = userProfileMapper.updateById(existingProfile);
            } else {
                savedCount = userProfileMapper.insert(userProfile);
            }

            meterRegistry.counter("resume.data.user_profile.saved").increment();
            return savedCount;
        } catch (Exception e) {
            log.error("保存用户基本信息失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.user_profile.error").increment();
            throw new RuntimeException("保存用户基本信息失败", e);
        }
    }

    private int saveContactMethods(Long userId, ThirdPartyParseResultDTO.ContactInfo contactInfo) {
        try {
            List<UserContactMethods> contactMethods = conversionService.convertContactInfo(userId, contactInfo);
            if (contactMethods.isEmpty()) return 0;

            List<UserContactMethods> newContacts = new ArrayList<>();
            for (UserContactMethods contact : contactMethods) {
                if (userContactMethodsMapper.selectByUserIdAndTypeAndValue(userId, contact.getContactType(), contact.getContactValue()) == null) {
                    newContacts.add(contact);
                }
            }

            if (newContacts.isEmpty()) return 0;

            int savedCount = batchDatabaseService.batchInsert(newContacts, userContactMethodsMapper).get();
            meterRegistry.counter("resume.data.contact_methods.saved").increment(savedCount);
            return savedCount;
        } catch (Exception e) {
            log.error("批量保存联系方式失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.contact_methods.error").increment();
            throw new RuntimeException("批量保存联系方式失败", e);
        }
    }

    private int saveEducationHistory(Long userId, List<ThirdPartyParseResultDTO.EducationExperience> educationList) {
        try {
            List<UserEducationHistory> educationHistory = conversionService.convertEducationExperience(userId, educationList);
            if (educationHistory.isEmpty()) return 0;

            List<UserEducationHistory> newEducations = new ArrayList<>();
            for (UserEducationHistory education : educationHistory) {
                if (userEducationHistoryMapper.selectByUserIdAndSchoolAndMajor(userId, education.getSchoolName(), education.getMajor()) == null) {
                    newEducations.add(education);
                }
            }

            if (newEducations.isEmpty()) return 0;

            int savedCount = batchDatabaseService.batchInsert(newEducations, userEducationHistoryMapper).get();
            meterRegistry.counter("resume.data.education_history.saved").increment(savedCount);
            return savedCount;
        } catch (Exception e) {
            log.error("批量保存教育经历失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.education_history.error").increment();
            throw new RuntimeException("批量保存教育经历失败", e);
        }
    }

    private int saveWorkHistory(Long userId, List<ThirdPartyParseResultDTO.WorkExperience> workList) {
        try {
            List<UserWorkHistory> workHistory = conversionService.convertWorkExperience(userId, workList);
            if (workHistory.isEmpty()) return 0;

            List<UserWorkHistory> newWorkHistory = new ArrayList<>();
            for (UserWorkHistory work : workHistory) {
                if (userWorkHistoryMapper.selectByUserIdAndCompanyAndPosition(userId, work.getCompanyName(), work.getPositionName()) == null) {
                    newWorkHistory.add(work);
                }
            }

            if (newWorkHistory.isEmpty()) return 0;

            int savedCount = batchDatabaseService.batchInsert(newWorkHistory, userWorkHistoryMapper).get();
            meterRegistry.counter("resume.data.work_history.saved").increment(savedCount);
            return savedCount;
        } catch (Exception e) {
            log.error("批量保存工作经历失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.work_history.error").increment();
            throw new RuntimeException("批量保存工作经历失败", e);
        }
    }

    private int saveProjectHistory(Long userId, List<ThirdPartyParseResultDTO.ProjectExperience> projectList) {
        try {
            List<UserProjectHistory> projectHistory = conversionService.convertProjectExperience(userId, projectList);
            if (projectHistory.isEmpty()) return 0;

            List<UserProjectHistory> newProjects = new ArrayList<>();
            for (UserProjectHistory project : projectHistory) {
                if (userProjectHistoryMapper.selectByUserIdAndProjectName(userId, project.getProjectName()) == null) {
                    newProjects.add(project);
                }
            }

            if (newProjects.isEmpty()) return 0;

            int savedCount = batchDatabaseService.batchInsert(newProjects, userProjectHistoryMapper).get();
            meterRegistry.counter("resume.data.project_history.saved").increment(savedCount);
            return savedCount;
        } catch (Exception e) {
            log.error("批量保存项目经历失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.project_history.error").increment();
            throw new RuntimeException("批量保存项目经历失败", e);
        }
    }

    private int saveSkills(Long userId, ThirdPartyParseResultDTO.Others others) {
        try {
            List<UserSkills> skills = conversionService.convertSkills(userId, others);
            if (skills.isEmpty()) return 0;

            List<UserSkills> newSkills = new ArrayList<>();
            for (UserSkills skill : skills) {
                if (userSkillsMapper.selectByUserIdAndSkillName(userId, skill.getSkillName()) == null) {
                    newSkills.add(skill);
                }
            }

            if (newSkills.isEmpty()) return 0;

            int savedCount = batchDatabaseService.batchInsert(newSkills, userSkillsMapper).get();
            meterRegistry.counter("resume.data.skills.saved").increment(savedCount);
            return savedCount;
        } catch (Exception e) {
            log.error("批量保存技能信息失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.skills.error").increment();
            throw new RuntimeException("批量保存技能信息失败", e);
        }
    }

    private int saveTrainingHistory(Long userId, List<ThirdPartyParseResultDTO.TrainingExperience> trainingList) {
        try {
            List<UserTraining> trainingHistory = conversionService.convertTrainingExperience(userId, trainingList);
            if (trainingHistory.isEmpty()) return 0;

            List<UserTraining> newTrainings = new ArrayList<>();
            for (UserTraining training : trainingHistory) {
                if (userTrainingMapper.selectByUserIdAndNameAndProvider(userId, training.getTrainingName(), training.getTrainingProvider()) == null) {
                    newTrainings.add(training);
                }
            }

            if (newTrainings.isEmpty()) return 0;

            int savedCount = batchDatabaseService.batchInsert(newTrainings, userTrainingMapper).get();
            meterRegistry.counter("resume.data.training_history.saved").increment(savedCount);
            return savedCount;
        } catch (Exception e) {
            log.error("批量保存培训经历失败: userId={}", userId, e);
            meterRegistry.counter("resume.data.training_history.error").increment();
            throw new RuntimeException("批量保存培训经历失败", e);
        }
    }
}
